<?php

namespace App\Service\Order;

use App\Constants\OrderConst;
use App\Http\Admin\Request\Order\OrderStockPrepareRequest;

use App\Model\Permission\User;
use App\Repository\Order\OrderItemRepository;
use App\Repository\Goods\GoodsSkuRepository;
use App\Repository\Goods\GoodsRepository;

use App\Service\IService;
use Hyperf\Database\Model\Builder;
use Hyperf\DbConnection\Db;

class OrderStatisticsService extends IService
{
    public function __construct(
        protected readonly OrderItemRepository $orderItemRepository,
        protected readonly GoodsSkuRepository $goodsSkuRepository,
        protected readonly GoodsRepository $goodsRepository,
    ) {
    }

    public function getRepository(): OrderItemRepository
    {
        return $this->orderItemRepository;
    }

    /**
     * 获取备货单数据
     * @param User $user 当前用户
     * @param OrderStockPrepareRequest $request 请求参数
     * @return array
     */
    public function getStockPrepareData(User $user, OrderStockPrepareRequest $request): array
    {
        // 获取用户有权限的店铺ID
        $userShopIds = $this->getBindShopIds($user);

        // 取交集，确保用户只能查询自己有权限的店铺
        $shopIds = array_intersect($request->shop_ids, $userShopIds);
        if (empty($shopIds)) {
            $shopIds = $userShopIds;
        }

        // 分页处理订单项数据，根据 goods_merge_type 和 sku_merge_type 合并
        $allData = [];
        $page = 1;
        $pageSize = 500;
        $endTime = date('Y-m-d H:i:s', time());

        do {
            // 分页查询未发货的订单项
            $orderItems = $this->getUnshippedOrderItemsPaginated($shopIds, $request, $page, $pageSize, $endTime);

            if (empty($orderItems)) {
                break;
            }

            // 根据合并类型处理当前页的数据
            $pageData = $this->processOrderItemsByMergeType($orderItems, $request->goods_merge_type, $request->sku_merge_type);

            // 合并到总数据中
            $allData = $this->mergeProcessedData($allData, $pageData);

            $page++;
        } while (count($orderItems) === $pageSize);

        // 根据 goods_merge_type 重新分组为层级结构
        $stockPrepareList = $this->groupByGoodsMergeType($allData, $request->goods_merge_type, $request->sku_merge_type);

        // 计算汇总信息
        $summary = $this->calculateSummary($stockPrepareList);

        return [
            'list' => $stockPrepareList,
            'summary' => $summary
        ];
    }

    /**
     * 分页获取未发货的订单项
     * @param array $shopIds 店铺ID数组
     * @param OrderStockPrepareRequest $request 请求参数
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    private function getUnshippedOrderItemsPaginated(array $shopIds, OrderStockPrepareRequest $request, int $page,
                                                     int $pageSize, string $endTime): array
    {
        $query = $this->orderItemRepository->getQuery();

        // 查询未发货的订单项
        $query->where('order_items.order_status', OrderConst::ORDER_STATUS_PAYMENT);
        $query->where('order_items.created_at', '<', $endTime);
        $query->whereIn('order_items.shop_id', $shopIds);

        // 时间范围筛选
        if ($request->start_time || $request->end_time) {
            $timeField = 'order_items.' . $request->time_field;

            if ($request->start_time) {
                $query->where($timeField, '>=', $request->start_time);
            }

            if ($request->end_time) {
                $query->where($timeField, '<=', $request->end_time);
            }
        }

        // 商品ID筛选
        if (!empty($request->include_goods_ids) || !empty($request->exclude_goods_ids)) {
            // 需要关联 goods_skus 表来筛选商品
            $query->leftJoin('goods_skus', function ($join) {
                $join->on('goods_skus.sku_id', '=', 'order_items.sku_id')
                     ->on('goods_skus.shop_id', '=', 'order_items.shop_id');
            });

            if (!empty($request->include_goods_ids)) {
                $query->whereIn('goods_skus.goods_id', $request->include_goods_ids);
            }

            if (!empty($request->exclude_goods_ids)) {
                $query->whereNotIn('goods_skus.goods_id', $request->exclude_goods_ids);
            }
        }

        // 分页查询
        $offset = ($page - 1) * $pageSize;
        $query->offset($offset)->limit($pageSize);

        // 选择需要的字段
        $query->select([
            'order_items.id',
            'order_items.sku_id',
            'order_items.shop_id',
            'order_items.sku_num',
            'order_items.sku_price',
            'order_items.total_fee',
            'order_items.goods_title',
            'order_items.sku_value',
            'order_items.sku_pic'
        ]);

        return $query->get()->toArray();
    }

    /**
     * 根据合并类型处理订单项数据
     * @param array $orderItems 订单项数据
     * @param int $goodsMergeType 商品合并类型
     * @param int $skuMergeType 规格合并类型
     * @return array
     */
    private function processOrderItemsByMergeType(array $orderItems, int $goodsMergeType, int $skuMergeType): array
    {
        if (empty($orderItems)) {
            return [];
        }

        // 提取所有 sku_id
        $skuIds = array_unique(array_column($orderItems, 'sku_id'));

        // 查询 goods_skus 信息
        $skuRelationData = $this->getSkuRelationData($skuIds);

        $result = [];

        foreach ($orderItems as $item) {
            $skuId = $item['sku_id'];

            if (!isset($skuRelationData[$skuId])) {
                continue;
            }

            $relationData = $skuRelationData[$skuId];

            // 根据合并类型生成合并键
            $mergeKey = $this->generateMergeKey($relationData, $goodsMergeType, $skuMergeType);

            if (!isset($result[$mergeKey])) {
                $result[$mergeKey] = [
                    'merge_key' => $mergeKey,
                    'sku_id' => $skuId,
                    'goods_id' => $relationData['goods_id'],
                    'goods_sku_id' => $relationData['goods_sku_id'],
                    'goods_title' => $relationData['goods_title'],
                    'custom_title' => $relationData['custom_title'],
                    'sku_value' => $relationData['sku_value'],
                    'custom_sku_value' => $relationData['custom_sku_value'],
                    'sku_pic' => $relationData['sku_pic'],
                    'outer_id' => $relationData['outer_id'],
                    'num_iid' => $relationData['num_iid'] ?? null,
                    'outer_goods_id' => $relationData['outer_goods_id'],
                    'total_quantity' => 0,
                    'total_amount' => 0,
                    'order_count' => 0,
                ];
            }

            $result[$mergeKey]['total_quantity'] += $item['sku_num'];
            $result[$mergeKey]['total_amount'] += $item['total_fee'];
            $result[$mergeKey]['order_count']++;
        }

        return $result;
    }

    /**
     * 根据合并类型生成合并键
     * @param array $relationData 关联数据
     * @param int $goodsMergeType 商品合并类型
     * @param int $skuMergeType 规格合并类型
     * @return string
     */
    private function generateMergeKey(array $relationData, int $goodsMergeType, int $skuMergeType): string
    {
        // 商品合并键
        $goodsKey = match ($goodsMergeType) {
            1 => $relationData['num_iid'] ?? '', // 根据num_iid合并
            2 => $relationData['goods_title'] ?? '', // 根据goods.goods_title合并
            3 => $relationData['outer_goods_id'] ?? '', // 根据goods.outer_goods_id合并
            default => '',
        };

        // SKU合并键
        $skuKey = match ($skuMergeType) {
            1 => $relationData['sku_id'] ?? '', // 根据sku_id合并
            2 => $relationData['sku_value'] ?? '', // 根据goods_skus.sku_value合并
            3 => $relationData['outer_id'] ?? '', // 根据goods_skus.outer_id合并
            default => $relationData['sku_id'] ?? '',
        };

        return $goodsKey . '|' . $skuKey;
    }

    /**
     * 获取SKU关联数据（只包括商品信息）
     * @param array $skuIds SKU ID数组
     * @return array
     */
    private function getSkuRelationData(array $skuIds): array
    {
        // 查询 goods_skus 和关联的 goods 信息
        $goodsSkusQuery = $this->goodsSkuRepository->getQuery()
            ->whereIn('goods_skus.sku_id', $skuIds)
            ->leftJoin('goods', 'goods.id', '=', 'goods_skus.goods_id')
            ->select([
                'goods_skus.id as goods_sku_id',
                'goods_skus.goods_id',
                'goods_skus.shop_id',
                'goods_skus.sku_id',
                'goods_skus.sku_value',
                'goods_skus.custom_sku_value',
                'goods_skus.sku_pic',
                'goods_skus.outer_id',
                'goods.goods_title',
                'goods.custom_title',
                'goods.goods_pic',
                'goods.num_iid',
                'goods.outer_goods_id'
            ]);

        $goodsSkus = $goodsSkusQuery->get()->toArray();

        // 合并数据
        $result = [];
        foreach ($goodsSkus as $goodsSku) {
            $skuId = $goodsSku['sku_id'];
            $goodsSkuId = $goodsSku['goods_sku_id'];

            $result[$skuId] = [
                'goods_sku_id' => $goodsSkuId,
                'goods_id' => $goodsSku['goods_id'],
                'sku_id' => $skuId,
                'sku_value' => $goodsSku['sku_value'],
                'custom_sku_value' => $goodsSku['custom_sku_value'],
                'sku_pic' => $goodsSku['sku_pic'],
                'outer_id' => $goodsSku['outer_id'],
                'goods_title' => $goodsSku['goods_title'],
                'custom_title' => $goodsSku['custom_title'],
                'num_iid' => $goodsSku['num_iid'],
                'outer_goods_id' => $goodsSku['outer_goods_id'],
            ];
        }

        return $result;
    }

    /**
     * 合并处理后的数据
     * @param array $allData 总数据
     * @param array $pageData 当前页数据
     * @return array
     */
    private function mergeProcessedData(array $allData, array $pageData): array
    {
        foreach ($pageData as $mergeKey => $data) {
            if (isset($allData[$mergeKey])) {
                $allData[$mergeKey]['total_quantity'] += $data['total_quantity'];
                $allData[$mergeKey]['total_amount'] += $data['total_amount'];
                $allData[$mergeKey]['order_count'] += $data['order_count'];
            } else {
                $allData[$mergeKey] = $data;
            }
        }

        return $allData;
    }

    /**
     * 根据商品合并类型分组数据
     * @param array $allData 所有数据
     * @param int $goodsMergeType 商品合并类型：1=根据num_iid合并，2=根据goods.goods_title合并，3=根据goods.outer_goods_id合并
     * @param int $skuMergeType SKU合并类型
     * @return array
     */
    private function groupByGoodsMergeType(array $allData, int $goodsMergeType, int $skuMergeType): array
    {
        $grouped = [];

        foreach ($allData as $data) {
            // 根据商品合并类型确定分组键和名称
            $groupKey = match ($goodsMergeType) {
                1 => $data['num_iid'] ?? '', // 根据num_iid合并
                2 => $data['goods_title'] ?? '', // 根据goods.goods_title合并
                3 => $data['outer_goods_id'] ?? '', // 根据goods.outer_goods_id合并
                default => $data['num_iid'] ?? '',
            };

            $groupName = match ($goodsMergeType) {
                1 => $data['custom_title'] ?: $data['goods_title'], // num_iid合并时显示商品标题
                2 => $data['goods_title'], // goods_title合并时显示商品标题
                3 => $data['outer_goods_id'] ?: ($data['custom_title'] ?: $data['goods_title']), // outer_goods_id合并时优先显示outer_goods_id，否则显示商品标题
                default => $data['custom_title'] ?: $data['goods_title'],
            };

            $groupId = $data['goods_id']; // 统一使用goods_id

            if (!isset($grouped[$groupKey])) {
                $grouped[$groupKey] = [
                    'numIid' => $data['num_iid'] ?? '',
                    'goodsPic' => $data['sku_pic'] ?? '', // 使用SKU图片作为商品图片
                    'goodsTitle' => $data['custom_title'] ?: $data['goods_title'],
                    'total_num' => 0,
                    'total_payment' => 0,
                    'skus' => []
                ];
            }

            // 根据SKU合并类型确定SKU显示信息
            $skuName = match ($skuMergeType) {
                1 => $data['sku_id'], // sku_id合并时显示sku_id
                2 => $data['custom_sku_value'] ?: $data['sku_value'], // sku_value合并时显示规格值
                3 => $data['outer_id'] ?: ($data['custom_sku_value'] ?: $data['sku_value']), // outer_id合并时优先显示outer_id，否则显示规格值
                default => $data['custom_sku_value'] ?: $data['sku_value'],
            };

            $grouped[$groupKey]['skus'][] = [
                'commodity_sku_id' => $data['sku_id'],
                'commodity_sku_name' => $skuName,
                'total_quantity' => $data['total_quantity'],
                'total_amount' => round($data['total_amount'], 2)
            ];

            // 累加商品级别的总数量和总金额
            $grouped[$groupKey]['total_num'] += $data['total_quantity'];
            $grouped[$groupKey]['total_payment'] += $data['total_amount'];
        }

        // 转换为数组并排序，对 total_payment 进行四舍五入
        $result = array_values($grouped);
        foreach ($result as &$item) {
            $item['total_payment'] = round($item['total_payment'], 2);
        }
        usort($result, function ($a, $b) {
            return strcmp($a['goodsTitle'], $b['goodsTitle']);
        });

        return $result;
    }

    /**
     * 计算汇总信息
     * @param array $stockPrepareList 备货单列表
     * @return array
     */
    private function calculateSummary(array $stockPrepareList): array
    {
        $totalSkuCount = 0;
        $totalQuantity = 0;
        $totalAmount = 0;
        $totalGoodsCount = count($stockPrepareList);

        foreach ($stockPrepareList as $commodity) {
            $totalSkuCount += count($commodity['skus']);

            foreach ($commodity['skus'] as $sku) {
                $totalQuantity += $sku['total_quantity'];
                $totalAmount += $sku['total_amount'];
            }
        }

        return [
            'total_sku_count' => $totalSkuCount,      // 总SKU数量
            'total_quantity' => $totalQuantity,       // 总商品数量
            'total_amount' => round($totalAmount, 2), // 总金额
            'total_goods_count' => $totalGoodsCount,  // 总商品/产品种类数
        ];
    }
}
