<?php

namespace App\Http\Admin\Request\Order;

use App\Http\Admin\Request\BaseFormRequest;
use Hyperf\Swagger\Annotation\Property;
use Hyperf\Swagger\Annotation\Schema;
use Hyperf\Swagger\Annotation\Items;

#[Schema(
    title: '备货单查询请求',
    properties: [
        new Property('shop_ids', description: '店铺ID数组', type: 'array', items: new Items(type: 'integer'), example: [1, 2, 3]),
        new Property('include_goods_ids', description: '包含的商品ID数组', type: 'array', items: new Items(type: 'integer'), example: [1, 2, 3]),
        new Property('exclude_goods_ids', description: '排除的商品ID数组', type: 'array', items: new Items(type: 'integer'), example: [4, 5, 6]),
        new Property('start_time', description: '开始时间', type: 'string', format: 'date-time', example: '2024-01-01 00:00:00'),
        new Property('end_time', description: '结束时间', type: 'string', format: 'date-time', example: '2024-12-31 23:59:59'),
        new Property('time_field', description: '时间字段类型：created_at=创建时间，updated_at=更新时间，pay_time=支付时间', type: 'string', example: 'created_at'),
        new Property('goods_merge_type', description: '商品合并类型：1=根据num_iid合并，2=根据goods.goods_title合并，3=根据goods.outer_goods_id合并', type: 'integer', example: 1),
        new Property('sku_merge_type', description: '规格合并类型：1=根据sku_id合并，2=根据goods_skus.goods_title合并，3=根据product_skus.outer_id合并', type: 'integer', example: 1),
    ]
)]
class OrderStockPrepareRequest extends BaseFormRequest
{
    public array $shop_ids = [];
    public array $include_goods_ids = [];
    public array $exclude_goods_ids = [];
    public ?string $start_time = null;
    public ?string $end_time = null;
    public string $time_field = 'created_at';
    public int $goods_merge_type = 0;
    public int $sku_merge_type = 0;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'shop_ids' => 'array|min:1',
            'shop_ids.*' => 'integer|min:1',
            'include_goods_ids' => 'sometimes|array',
            'include_goods_ids.*' => 'integer|min:1',
            'exclude_goods_ids' => 'sometimes|array',
            'exclude_goods_ids.*' => 'integer|min:1',
            'start_time' => 'sometimes|date_format:Y-m-d H:i:s',
            'end_time' => 'sometimes|date_format:Y-m-d H:i:s',
            'time_field' => 'sometimes|string|in:created_at,updated_at,pay_time',
            'goods_merge_type' => 'sometimes|integer|in:1,2,3',
            'sku_merge_type' => 'sometimes|integer|in:1,2,3',
        ];
    }

    public function attributes(): array
    {
        return [
            'shop_ids' => '店铺ID数组',
            'shop_ids.*' => '店铺ID',
            'include_goods_ids' => '包含的商品ID数组',
            'include_goods_ids.*' => '商品ID',
            'exclude_goods_ids' => '排除的商品ID数组',
            'exclude_goods_ids.*' => '商品ID',
            'start_time' => '开始时间',
            'end_time' => '结束时间',
            'time_field' => '时间字段',
            'goods_merge_type' => '商品合并类型',
            'sku_merge_type' => '规格合并类型',
        ];
    }

    public function messages(): array
    {
        return [
            'shop_ids.required' => '店铺ID数组不能为空',
            'shop_ids.array' => '店铺ID必须是数组格式',
            'shop_ids.min' => '至少需要选择一个店铺',
            'shop_ids.*.integer' => '店铺ID必须是整数',
            'shop_ids.*.min' => '店铺ID必须大于0',
            'include_goods_ids.array' => '包含商品ID必须是数组格式',
            'include_goods_ids.*.integer' => '商品ID必须是整数',
            'include_goods_ids.*.min' => '商品ID必须大于0',
            'exclude_goods_ids.array' => '排除商品ID必须是数组格式',
            'exclude_goods_ids.*.integer' => '商品ID必须是整数',
            'exclude_goods_ids.*.min' => '商品ID必须大于0',
            'start_time.date_format' => '开始时间格式不正确，应为：Y-m-d H:i:s',
            'end_time.date_format' => '结束时间格式不正确，应为：Y-m-d H:i:s',
            'time_field.in' => '时间字段只能是：created_at、updated_at、pay_time',
            'goods_merge_type.in' => '商品合并类型只能是：1、2、3',
            'sku_merge_type.in' => '规格合并类型只能是：1、2、3',
        ];
    }
}
